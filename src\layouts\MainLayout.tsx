import React, { useEffect, useState } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { MapIcon, LayoutDashboard, BarChart2, <PERSON><PERSON>hart, <PERSON>ch, Settings, Vegan, <PERSON>lipboardList, TriangleAlert, Zap, Box, <PERSON><PERSON>hart, Wind , Info, Book} from 'lucide-react';
import { FloatingChat } from '../pages/AltoChatBot/components/floating-chat';
import { RouteTransition } from '../components/RouteTransition';
import altoLogo from '@/assets/alto_logo.svg';
import { format } from 'date-fns';
import UserMenu from '../components/UserMenu';
import Weather from '../components/Weather';
import { useAuth } from '@/contexts/AuthContext';
import { loadMapConfig, getMedia } from '@/services/mapService';
import { getTabVisibilitySettings, isTabVisibleToUser, TabVisibility } from '@/services/tabVisibilityService';

interface NavigationItemForMenu {
  path: string;
  label: string;
  icon: React.ElementType;
}

interface NavMenuItemProps {
  item: NavigationItemForMenu;
  isActive: boolean;
}

const NavMenuItem: React.FC<NavMenuItemProps> = ({ item, isActive }) => {
  const { path, icon: Icon, label } = item;
  const [isHovered, setIsHovered] = useState(false);

  const baseClasses = `
    relative flex items-center rounded-lg
    transition-all duration-200 group w-[56px] h-[56px]
    flex-col p-1 gap-0.5 justify-center
    text-gray-500 hover:text-[#0E7EE4]
  `;

  const activeClasses = `
    bg-[linear-gradient(251deg,rgba(139,198,255,0.40)_0%,rgba(203,240,239,0.20)_100%)]
    text-[#0E7EE4]
  `;

  const hoverClasses = `
    hover:bg-blue-50/50
  `;

  const iconWrapperBaseClasses = `
    p-1 rounded-md transition-all duration-200 relative
  `;

  const iconWrapperActiveClasses = `
     bg-white/80 shadow-sm
  `;

  const iconWrapperHoverClasses = `
    group-hover:bg-[linear-gradient(251deg,rgba(139,198,255,0.20)_0%,rgba(203,240,239,0.10)_100%)]
    group-hover:shadow-[0_1px_2px_rgba(14,125,228,0.08)]
  `;

  const iconClasses = `
    flex-shrink-0 transition-transform duration-200 w-4 h-4
    ${isHovered && !isActive ? 'scale-110' : ''}
    ${isActive ? 'stroke-[#0E7EE4]' : isHovered ? 'stroke-[#0E7EE4]' : 'stroke-[#9CA3AF] group-hover:stroke-[#0E7EE4]'}
  `;

  const labelClasses = `
    text-[8px] mt-0.5 text-center font-bold font-poppins
    transition-all duration-200 leading-none
    ${isActive ? 'text-[#0E7EE4]' : isHovered ? 'text-[#0E7EE4]' : 'text-[#9CA3AF] group-hover:text-[#0E7EE4]'}
  `;

  const activeIndicatorClasses = `
    absolute left-0 top-1/2 -translate-y-1/2 w-[3px] h-6 rounded-r-md bg-[#0E7EE4]
  `;

  return (
    <Link
      to={path}
      onClick={(e) => {
        if (isActive) {
          e.preventDefault();
        }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`${baseClasses} ${isActive ? activeClasses : hoverClasses}`}
    >
      {isActive && (
        <div className={activeIndicatorClasses} />
      )}

      <div className={`
        ${iconWrapperBaseClasses}
        ${isActive ? iconWrapperActiveClasses : iconWrapperHoverClasses}
      `}>
        <Icon size={16} className={iconClasses} strokeWidth={1.5} />
      </div>

      <span className={labelClasses}>
        {label}
      </span>
    </Link>
  );
};

const MainLayout: React.FC = () => {
  const { isAuthenticated, isLoading, userRole, isSuperuser } = useAuth();
  const location = useLocation();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [headerLogo, setHeaderLogo] = useState<string | null>(null);
  const [tabVisibilitySettings, setTabVisibilitySettings] = useState<TabVisibility[]>([]);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const loadTabVisibilitySettings = async () => {
      try {
        setIsLoadingSettings(true);
        const settings = await getTabVisibilitySettings();
        console.log('Tab visibility settings loaded in MainLayout:', settings);
        setTabVisibilitySettings(settings);
      } catch (error) {
        console.error('Error loading tab visibility settings:', error);
      } finally {
        setIsLoadingSettings(false);
      }
    };

    if (isAuthenticated) {
      loadTabVisibilitySettings();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    const fetchHeaderLogo = async () => {
      try {
        const currentMapId = localStorage.getItem('currentMapId') || '1';
        if (currentMapId) {
          const config = await loadMapConfig(currentMapId);

          if (config.properties.headerLogo) {
            const mediaBlob = await getMedia(config.properties.headerLogo);
            const url = URL.createObjectURL(mediaBlob);
            setHeaderLogo(url);
          } else {
            setHeaderLogo(null);
          }
        }
      } catch (error) {
        console.error('Error loading header logo:', error);
      }
    };

    if (isAuthenticated) {
      fetchHeaderLogo();
    }

    return () => {
      if (headerLogo) {
        URL.revokeObjectURL(headerLogo);
      }
    };
  }, [isAuthenticated]);

  const isActive = (path: string) => {
    return location.pathname === path ||
           (location.pathname === '/' && path === '/map');
  };

  const getPageName = () => {
    const currentPath = location.pathname;
    const currentNavItem = allNavigationItems.find(item => currentPath.includes(item.path));
    if (currentNavItem?.pageName) {
      return currentNavItem.pageName;
    }
    const lastSegment = currentPath.split('/').pop();
    if (!lastSegment) return 'Home';
    return lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1);
  };

  const isTabVisible = (tabId: string) => {
    if (tabVisibilitySettings.length === 0 || isLoadingSettings) {
      return true;
    }

    return isTabVisibleToUser(tabId, userRole || undefined, isSuperuser || false, tabVisibilitySettings);
  };

  const allNavigationItems = [
    {
      path: '/app/autopilot',
      label: 'Autopilot',
      pageName: 'Autopilot Dashboard',
      icon: Zap,
      envKey: 'VITE_SHOW_AUTOPILOT',
      id: 'autopilot'
    },
    {
      path: '/app/map',
      label: 'Plant',
      pageName: 'Chiller Plant Map',
      icon: MapIcon,
      envKey: 'VITE_SHOW_MAP',
      id: 'map'
    },
    {
      path: '/app/air-side',
      label: 'Air Side',
      pageName: 'Air Side Control',
      icon: Wind,
      envKey: 'VITE_SHOW_AIR_SIDE',
      id: 'air-side'
    },
    {
      path: '/app/overview-dashboard',
      label: 'Dashboard',
      pageName: 'Plant Dashboard',
      icon: LayoutDashboard,
      envKey: 'VITE_SHOW_DASHBOARD',
      id: 'overview-dashboard'
    },
    {
      path: '/app/historical-data',
      label: 'Historical',
      pageName: 'Historical Data Visualization',
      icon: LineChart,
      envKey: 'VITE_SHOW_HISTORICAL_DATA',
      id: 'historical-data'
    },
    {
      path: '/app/maintenance',
      label: 'Maintenance',
      pageName: 'Equipment Maintenance',
      icon: Wrench,
      envKey: 'VITE_SHOW_MAINTENANCE',
      id: 'maintenance'
    },
    {
      path: '/app/analytics',
      label: 'Analytics',
      pageName: 'Plant Analytics',
      icon: BarChart2,
      envKey: 'VITE_SHOW_ANALYTICS',
      id: 'analytics'
    },
    {
      path: '/app/settings',
      label: 'Settings',
      pageName: 'System Settings',
      icon: Settings,
      envKey: 'VITE_SHOW_SETTINGS',
      id: 'settings'
    },
    {
      path: '/app/saving-report',
      label: 'Saving Report',
      pageName: 'Saving Report',
      icon: Vegan,
      envKey: 'VITE_SHOW_SAVING_REPORT',
      id: 'saving-report'
    },
    {
      path: '/app/technical-report',
      label: 'Technical Report',
      pageName: 'Technical Report',
      icon: ClipboardList,
      envKey: 'VITE_SHOW_TECHNICAL_REPORT',
      id: 'technical-report'
    },
    {
      path: '/app/afdd',
      label: 'AFDD',
      pageName: 'AFDD',
      icon: TriangleAlert,
      envKey: 'VITE_SHOW_AFDD',
      id: 'afdd'
    },
    {
      path: '/app/room-analytics',
      label: 'Room Analytics',
      pageName: 'Room Analytics',
      icon: PieChart,
      envKey: 'VITE_SHOW_ROOM_ANALYTICS',
      id: 'room-analytics'
    }
  ];

  const navigationItems = allNavigationItems.filter(item => {
    const envValue = import.meta.env[item.envKey];
    const envAllows = envValue === undefined || envValue === 'true' || envValue === true;

    const visibilityAllows = isTabVisible(item.id);

    return envAllows && visibilityAllows;
  });

  if (isLoading) {
    return <div className="w-screen h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="fixed top-0 left-[56px] right-0 z-50 bg-white border-b border-[#DBE4FF]">
        <div className="w-full h-[44px] px-4 py-2 flex items-center justify-between">
          <div className="flex items-center gap-3">
            {headerLogo && (
              <img
                src={headerLogo}
                alt="Customer Logo"
                className="h-[27px] max-w-[80px] w-auto object-cover rounded bg-card border border-[#EDEFF9]"
              />
            )}
            <div className="text-lg font-semibold font-poppins tracking-[0.01em] bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] bg-clip-text text-transparent">
              {getPageName()}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Weather />
            <div className="flex items-center gap-[6px]">
              <div className="text-[10px] text-[#0E7EE4] font-poppins">
                {format(currentTime, 'EEE, dd MMM, yyyy')}
              </div>
              <div className="text-[10px] text-[#0E7EE4] font-poppins">
                {format(currentTime, 'HH:mm:ss')}
              </div>
            </div>
            <div className="h-6 w-px bg-[#EDEFF9]" />
            <Link
              to="/app/manual"
              className="p-1.5 rounded-lg hover:bg-white/75 hover:shadow-sm transition-all duration-200 group"
              title="Software Manual"
            >
              <div className="relative">
                <Book size={24} className="text-muted group-hover:text-[#0E7EE4] transition-colors" />
                <div className="absolute top-[40%] left-1/2 -translate-x-1/2 -translate-y-1/2 text-muted group-hover:text-[#0E7EE4] transition-colors font-serif italic font-bold text-sm">
                  i
                </div>
              </div>
            </Link>
            <UserMenu />
          </div>
        </div>
      </header>

      <div className="flex pt-[44px]">
        <aside className="fixed left-0 top-[0px] bottom-0 w-[56px] bg-white shadow-[4px_1px_10px_rgba(57,124,221,0.08)] border-r border-[#DBE4FF] flex flex-col items-start pt-10 pb-6 gap-10 z-40">
          <div className="w-full h-[40px] py-1 px-4 flex justify-center items-center">
            <img className="w-6 h-[21.31px]" src={altoLogo} alt="logo" />
          </div>

          <div className="flex flex-col gap-1">
            {navigationItems.map((item) => {
              const isCurrentPath = isActive(item.path);
              const navMenuItem: NavigationItemForMenu = {
                path: item.path,
                label: item.label,
                icon: item.icon,
              };
              return (
                <NavMenuItem
                  key={item.path}
                  item={navMenuItem}
                  isActive={isCurrentPath}
                />
              );
            })}
          </div>
        </aside>

        <main className="flex-1 ml-[56px] w-[calc(100vw-56px)]">
          <RouteTransition />
        </main>
      </div>

      <footer className="fixed bottom-0 left-[56px] right-0 h-[27px] px-4 bg-[#F9FAFF]/80 border-t border-[#DBE4FF] backdrop-blur-[12px] flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="flex items-center">
            <span className="text-[#5E5E5E] text-[10px] font-poppins leading-none">Powered by</span>
            <span className="text-[#0E7EE4] text-[10px] font-poppins leading-none">&nbsp;AltoTech Global</span>
          </div>
          <div className="text-[#5E5E5E] text-[10px] font-poppins leading-none">Version v.1.1.2025</div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout;